import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    const { id } = params

    // Get blog post with author and categories
    const { data: post, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        profiles:author_id (
          name,
          email
        ),
        post_categories (
          blog_categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching blog post:', error)
      return NextResponse.json({ error: 'Blog post not found' }, { status: 404 })
    }

    // Check if post is published or user is the author
    const { data: { user } } = await supabase.auth.getUser()
    
    if (post.status !== 'published' && (!user || user.id !== post.author_id)) {
      return NextResponse.json({ error: 'Blog post not found' }, { status: 404 })
    }

    return NextResponse.json({ post })

  } catch (error) {
    console.error('Error in blog GET by ID:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { title, content, featured_image, status, categories = [] } = body

    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 })
    }

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Get current post to check published_at
    const { data: currentPost } = await supabase
      .from('blog_posts')
      .select('published_at, author_id')
      .eq('id', id)
      .single()

    if (!currentPost) {
      return NextResponse.json({ error: 'Blog post not found' }, { status: 404 })
    }

    // Check if user is the author
    if (currentPost.author_id !== user.id) {
      return NextResponse.json({ error: 'You can only edit your own posts' }, { status: 403 })
    }

    // Update blog post
    const updateData: any = {
      title,
      slug,
      content,
      featured_image,
      status
    }

    // Set published_at if status is published and it wasn't published before
    if (status === 'published' && !currentPost.published_at) {
      updateData.published_at = new Date().toISOString()
    }

    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (postError) {
      console.error('Error updating blog post:', postError)
      return NextResponse.json({ error: 'Failed to update blog post' }, { status: 500 })
    }

    // Update categories
    // First, remove existing categories
    await supabase
      .from('post_categories')
      .delete()
      .eq('post_id', id)

    // Then add new categories
    if (categories.length > 0) {
      const categoryInserts = categories.map((categoryId: string) => ({
        post_id: id,
        category_id: categoryId
      }))

      const { error: categoryError } = await supabase
        .from('post_categories')
        .insert(categoryInserts)

      if (categoryError) {
        console.error('Error updating categories:', categoryError)
      }
    }

    return NextResponse.json({ post })

  } catch (error) {
    console.error('Error in blog PUT by ID:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params

    // Check if user is the author or admin
    const { data: post } = await supabase
      .from('blog_posts')
      .select('author_id')
      .eq('id', id)
      .single()

    if (!post) {
      return NextResponse.json({ error: 'Blog post not found' }, { status: 404 })
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.role === 'admin'
    const isAuthor = post.author_id === user.id

    if (!isAdmin && !isAuthor) {
      return NextResponse.json({ error: 'You can only delete your own posts' }, { status: 403 })
    }

    // Delete blog post (categories will be deleted automatically due to CASCADE)
    const { error: deleteError } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting blog post:', deleteError)
      return NextResponse.json({ error: 'Failed to delete blog post' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Blog post deleted successfully' })

  } catch (error) {
    console.error('Error in blog DELETE by ID:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
