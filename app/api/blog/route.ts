import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const status = searchParams.get('status') || 'published'
    const search = searchParams.get('search')
    
    const offset = (page - 1) * limit

    // Build query
    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        profiles:author_id (
          name,
          email
        ),
        post_categories (
          blog_categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('status', status)
      .order('published_at', { ascending: false })

    // Add search filter
    if (search) {
      query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`)
    }

    // Add category filter
    if (category) {
      query = query.eq('post_categories.blog_categories.slug', category)
    }

    // Get total count
    const { count } = await supabase
      .from('blog_posts')
      .select('*', { count: 'exact', head: true })
      .eq('status', status)

    // Get paginated results
    const { data: posts, error } = await query
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching blog posts:', error)
      return NextResponse.json({ error: 'Failed to fetch blog posts' }, { status: 500 })
    }

    return NextResponse.json({
      posts: posts || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })

  } catch (error) {
    console.error('Error in blog GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { title, content, featured_image, status = 'draft', categories = [] } = body

    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 })
    }

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Create blog post
    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .insert({
        author_id: user.id,
        title,
        slug,
        content,
        featured_image,
        status,
        published_at: status === 'published' ? new Date().toISOString() : null
      })
      .select()
      .single()

    if (postError) {
      console.error('Error creating blog post:', postError)
      return NextResponse.json({ error: 'Failed to create blog post' }, { status: 500 })
    }

    // Add categories if provided
    if (categories.length > 0) {
      const categoryInserts = categories.map((categoryId: string) => ({
        post_id: post.id,
        category_id: categoryId
      }))

      const { error: categoryError } = await supabase
        .from('post_categories')
        .insert(categoryInserts)

      if (categoryError) {
        console.error('Error adding categories:', categoryError)
      }
    }

    return NextResponse.json({ post }, { status: 201 })

  } catch (error) {
    console.error('Error in blog POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, title, content, featured_image, status, categories = [] } = body

    if (!id || !title || !content) {
      return NextResponse.json({ error: 'ID, title and content are required' }, { status: 400 })
    }

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Update blog post
    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .update({
        title,
        slug,
        content,
        featured_image,
        status,
        published_at: status === 'published' && !post?.published_at ? new Date().toISOString() : undefined
      })
      .eq('id', id)
      .eq('author_id', user.id) // Ensure user can only update their own posts
      .select()
      .single()

    if (postError) {
      console.error('Error updating blog post:', postError)
      return NextResponse.json({ error: 'Failed to update blog post' }, { status: 500 })
    }

    // Update categories
    // First, remove existing categories
    await supabase
      .from('post_categories')
      .delete()
      .eq('post_id', id)

    // Then add new categories
    if (categories.length > 0) {
      const categoryInserts = categories.map((categoryId: string) => ({
        post_id: id,
        category_id: categoryId
      }))

      const { error: categoryError } = await supabase
        .from('post_categories')
        .insert(categoryInserts)

      if (categoryError) {
        console.error('Error updating categories:', categoryError)
      }
    }

    return NextResponse.json({ post })

  } catch (error) {
    console.error('Error in blog PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Post ID is required' }, { status: 400 })
    }

    // Delete blog post (categories will be deleted automatically due to CASCADE)
    const { error: deleteError } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id)
      .eq('author_id', user.id) // Ensure user can only delete their own posts

    if (deleteError) {
      console.error('Error deleting blog post:', deleteError)
      return NextResponse.json({ error: 'Failed to delete blog post' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Blog post deleted successfully' })

  } catch (error) {
    console.error('Error in blog DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
