"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Save, Eye, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { ImageUpload } from "@/components/ui/image-upload"

const blogSchema = z.object({
  title: z.string().min(1, "Judul wajib diisi"),
  content: z.string().min(1, "Konten wajib diisi"),
  featured_image: z.string().optional(),
  status: z.enum(['draft', 'published', 'archived']),
  categories: z.array(z.string()).optional(),
})

type BlogFormData = z.infer<typeof blogSchema>

interface BlogCategory {
  id: string
  name: string
  slug: string
}

interface BlogFormProps {
  mode: "create" | "edit"
  postId?: string
  initialData?: {
    id: string
    title: string
    content: string
    featured_image?: string
    status: string
    post_categories?: Array<{
      blog_categories: {
        id: string
        name: string
        slug: string
      }
    }>
  }
}

export function BlogForm({ mode, postId, initialData }: BlogFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(false)
  const [categories, setCategories] = useState<BlogCategory[]>([])

  const form = useForm<BlogFormData>({
    resolver: zodResolver(blogSchema),
    defaultValues: {
      title: "",
      content: "",
      featured_image: "",
      status: "draft",
      categories: [],
    },
  })

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await fetch('/api/blog/categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data.categories || [])
        }
      } catch (error) {
        console.error('Error loading categories:', error)
      }
    }

    loadCategories()
  }, [])

  // Load post data for edit mode
  useEffect(() => {
    if (mode === "edit" && postId && !initialData) {
      const fetchPostData = async () => {
        setLoadingData(true)
        try {
          const response = await fetch(`/api/blog/${postId}`)
          if (response.ok) {
            const data = await response.json()
            const post = data.post
            
            if (post) {
              form.setValue("title", post.title || "")
              form.setValue("content", post.content || "")
              form.setValue("featured_image", post.featured_image || "")
              form.setValue("status", post.status || "draft")
              
              // Set categories
              const postCategories = post.post_categories?.map((pc: any) => pc.blog_categories.id) || []
              form.setValue("categories", postCategories)
            }
          }
        } catch (error) {
          console.error("Error fetching post data:", error)
        } finally {
          setLoadingData(false)
        }
      }

      fetchPostData()
    } else if (initialData) {
      form.setValue("title", initialData.title || "")
      form.setValue("content", initialData.content || "")
      form.setValue("featured_image", initialData.featured_image || "")
      form.setValue("status", initialData.status as any || "draft")
      
      const postCategories = initialData.post_categories?.map(pc => pc.blog_categories.id) || []
      form.setValue("categories", postCategories)
    }
  }, [mode, postId, initialData, form])

  const onSubmit = async (data: BlogFormData) => {
    setLoading(true)
    try {
      const url = mode === "create" ? "/api/blog" : `/api/blog/${postId}`
      const method = mode === "create" ? "POST" : "PUT"
      
      const payload = mode === "edit" 
        ? { id: postId, ...data }
        : data

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        const result = await response.json()
        
        if (mode === "create") {
          router.push(`/dashboard/blog`)
        } else {
          router.push(`/dashboard/blog`)
        }
      } else {
        const error = await response.json()
        console.error("Failed to save post:", error)
        alert(error.error || "Gagal menyimpan artikel")
      }
    } catch (error) {
      console.error("Error saving post:", error)
      alert("Terjadi kesalahan saat menyimpan artikel")
    } finally {
      setLoading(false)
    }
  }

  const handleImageUpload = (data: any) => {
    if (data.file_path) {
      form.setValue("featured_image", data.file_path)
    }
  }

  if (loadingData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Memuat data artikel...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/blog">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === "create" ? "Tulis Artikel Baru" : "Edit Artikel"}
          </h1>
          <p className="text-muted-foreground">
            {mode === "create" ? "Buat artikel baru untuk blog" : "Edit artikel yang sudah ada"}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {mode === "create" ? "Artikel Baru" : "Edit Artikel"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Judul Artikel</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan judul artikel" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Konten</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Tulis konten artikel di sini..."
                        rows={15}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="featured_image"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gambar Utama</FormLabel>
                    <FormControl>
                      <ImageUpload
                        imageKey="blog_featured"
                        category="blog"
                        currentImage={field.value}
                        onUploadSuccess={handleImageUpload}
                        label="Upload Gambar Artikel"
                        description="Upload gambar utama untuk artikel ini"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="categories"
                  render={() => (
                    <FormItem>
                      <FormLabel>Kategori</FormLabel>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {categories.map((category) => (
                          <FormField
                            key={category.id}
                            control={form.control}
                            name="categories"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={category.id}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(category.id)}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([...(field.value || []), category.id])
                                          : field.onChange(
                                              field.value?.filter(
                                                (value) => value !== category.id
                                              )
                                            )
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    {category.name}
                                  </FormLabel>
                                </FormItem>
                              )
                            }}
                          />
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex gap-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={loading}
                >
                  Batal
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {mode === "create" ? "Publish Artikel" : "Simpan Perubahan"}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
